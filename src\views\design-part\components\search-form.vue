<template>
  <NeDynamicForm
    :schema="formSchema"
    :model="formData"
    ref="ruleFormRef"
    label-width="100px"
    class="design-form"
    @onValuesChange="handleFormChange"
  >
    <div class="flex w-full justify-center">
      <el-button type="primary" @click="handleSearch">查询</el-button>
      <el-button @click="handleReset">重置</el-button>
    </div>
  </NeDynamicForm>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits, defineExpose, computed, watch } from 'vue'
import NeDynamicForm, {
  DynamicFormExposeProps,
  SchemaItemProps
} from '@/component-design/ne-dynamic-form/index.vue'
import { PartTypeEmu, StatusEmu, SubcategoryEmu } from '@/constant'
import { keys } from 'lodash-es'

// 定义组件 props
const props = defineProps({
  responsiblePersons: {
    type: Array<{ label: string; value: string | number }>,
    default: () => []
  }
})

// 定义事件
const emit = defineEmits(['search', 'reset'])

// 表单引用
const ruleFormRef = ref<DynamicFormExposeProps>()

// 表单数据
const formData = reactive({
  partNumber: '',
  partName: '',
  status: '',
  subCategory: '',
  updateTime: ''
})

// 表单架构
const formSchema = computed<SchemaItemProps[]>(() => [
  {
    type: 'input',
    label: '零部件编号',
    prop: 'partNo',
    placeholder: '请输入零部件编号',
    span: 8
  },
  {
    type: 'select',
    label: '状态',
    prop: 'status',
    placeholder: '请选择状态',
    span: 8,
    options: keys(StatusEmu).map((key) => ({
      label: StatusEmu[key].text,
      value: key
    }))
  },
  {
    type: 'select',
    label: '子类别',
    prop: 'partType',
    placeholder: '请选择子类别',
    span: 8,
    options: keys(PartTypeEmu).map((key) => ({
      label: PartTypeEmu[key].text,
      value: key
    }))
  },
  {
    type: 'input',
    label: '零部件名称',
    prop: 'partName',
    placeholder: '请输入零部件名称',
    span: 8
  },
  {
    type: 'select',
    label: '责任人',
    prop: 'owner.ncid',
    placeholder: '请选择责任人',
    span: 8,
    options: props.responsiblePersons // 这里会自动响应props.responsiblePersons的变化
  },
  {
    type: 'daterange',
    label: '更新时间',
    prop: 'updateTime',
    span: 8,
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  }
])

// 表单值变化处理
const handleFormChange = (val: any) => {
  Object.assign(formData, val)
}

// 查询处理
const handleSearch = () => {
  if (!ruleFormRef.value) return
  const { getFormData } = ruleFormRef.value
  const formValues = getFormData()
  emit('search', formValues)
}

// 重置处理
const handleReset = () => {
  if (!ruleFormRef.value) return
  const { formIns } = ruleFormRef.value
  formIns?.resetFields()
  emit('reset')
}

// 暴露方法
defineExpose({
  getFormData: () => {
    if (!ruleFormRef.value) return {}
    const { getFormData } = ruleFormRef.value
    return getFormData()
  },
  resetForm: () => {
    if (!ruleFormRef.value) return
    const { formIns } = ruleFormRef.value
    formIns?.resetFields()
  }
})
</script>

<style scoped></style>

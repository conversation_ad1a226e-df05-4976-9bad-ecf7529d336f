<template>
  <div class="w-0 flex-1 px-6 bg-[#ECEEF3]">
    <div class="rounded-[8px] overflow-hidden bg-white mt-5">
      <Page title="设计零部件" :closable="false"></Page>
      <div class="px-6">
        <SearchForm
          ref="searchFormRef"
          :responsiblePersons="responsiblePersons"
          @search="handleSearch"
          @reset="handleReset"
        />
      </div>
    </div>
    <div class="bg-white px-6 py-5 rounded-[8px] mt-3">
      <ny-table
        :data="tableData"
        :columns="tableColumns"
        max-height="calc(100vh - 400px)"
      >
        <template #status="{ row }">
          <el-tag :type="StatusEmu[row.status]?.type">
            {{ StatusEmu[row.status]?.text || row.status }}
          </el-tag>
        </template>
        <template #subcategory="{ row }">
          <el-text>
            {{ SubcategoryEmu[row.status]?.text }}
          </el-text>
        </template>
        <template #partNo="{ row }">
          <div
            class="part-no-container flex justify-between items-center cursor-pointer"
            @click="openPartDetail(row)"
          >
            <el-text type="primary">
              {{ row.partNo }}
            </el-text>
            <el-tag
              v-if="row.partType === PartType.NeueASM"
              class="bom-tag"
              size="small"
              effect="plain"
              type="info"
              @click.stop="openBom(row)"
              >BOM</el-tag
            >
          </div>
        </template>
        <template #responsible="{ row }">
          <ne-avatar :name="row.responsible" />
        </template>
        <template #modifiedAt="{ row }"
          >{{ dayjs(row.modifiedAt).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
        <template #owner="{ row }">
          <ne-avatar :name="row.owner?.name" />
        </template>
        <template #version="{ row }">
          {{ row.version }}
          <el-tag class="ml-2" v-if="row.latestVersion" type="primary">最新版</el-tag>
        </template>
      </ny-table>
    </div>
    <ny-drawer v-model="partVisible" size="80%">
      <template #header>
        <div class="flex items-center">
          <i class="iconfont icon-a-zhuangpei1 !text-[24px] text-[#B370FF] mr-3"></i>
          <h4 class="font-medium text-[24px]">{{ selectedPart?.partNo }}</h4>
          <div
            class="text-[#1856EB] ml-3 cursor-pointer"
            @click="openBom(selectedPart)"
          >
            <i class="iconfont icon-Launch mr-1 text-[14px]"></i> 查看BOM
          </div>
        </div>
      </template>
      <div class="mx-[-20px]">
        <PartDetail v-if="partVisible" :partId="selectedPart?.ncid" />
      </div>
    </ny-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from '@vue/runtime-core'
import { ElMessage } from 'element-plus'
import SearchForm from './components/search-form.vue'
import Page from '@/components/layout/page.vue'
import PartDetail from './components/part-detail.vue'
import { StatusEmu, SubcategoryEmu } from '@/constant'
import actions from '@/service/actions'
import dayjs from 'dayjs'
import { Plt0CADBOMResponseList, PartType } from '.'
import { useRouter } from 'vue-router'
import { transformData } from '@/utils'

// 表单引用
const searchFormRef = ref()
const partVisible = ref(false)
const selectedPart = ref<any>(null)
const filters = ref({})

// 责任人选项
const responsiblePersons = ref()
// 表格列配置
const tableColumns = [
  { prop: 'partNo', label: '零部件编号' },
  { prop: 'partName', label: '零部件名称' },
  { prop: 'partType', label: '子类别' },
  { prop: 'version', label: '版本号' },
  { prop: 'status', label: '状态' },
  { prop: 'owner', label: '修改人' },
  { prop: 'modifiedAt', label: '修改时间', sortable: true },
  { prop: 'owner', label: '责任人' }
]

const router = useRouter()
const tableData = ref<Plt0CADBOMResponseList[]>()
let openBom = (row: any) => {
  router.push({ path: '/bom', query: { ncid: row.ncid } })
}
// 处理搜索
const handleSearch = (values: any) => {
  let newValues = {
    ...values,
    createdAt: values?.updateTime[0] || '',
    modifedAt: values?.updateTime[1] || ''
  }
  delete newValues.updateTime
  filters.value = newValues
}

// 处理重置
const handleReset = () => {
  ElMessage.info('表单已重置')
}

// 打开零部件详情
const openPartDetail = (row: any) => {
  selectedPart.value = row
  partVisible.value = true
}
let getResponsiblePersons = async () => {
  let res = await actions.postUserDCSRelations({
    pageParams: {
      limit: 99999,
      page: 0
    }
  })
  console.log(res)
  responsiblePersons.value = res.data.map(
    (item: { source: { ncid: any; name: any } }) => {
      return {
        value: item.source.ncid,
        label: item.source.name
      }
    }
  )
}
let getDesignPartList = async (condition?: any) => {
  let res = await actions.postDesignPartList({
    classId: 'plt0NeueCADPart',
    pageParams: {
      limit: 9999,
      page: '1',
      sorts: null
    },
    expands: [
      {
        referenceField: 'createdBy'
      },
      {
        referenceField: 'owner'
      }
    ],
    condition
  })
  tableData.value = res.data
}
watch(filters, (val) => {
  let condition = transformData(val, {
    partNo: 'like',
    partName: 'like',
    createdAt: '>=',
    modifedAt: '<='
  })
  getDesignPartList(condition)
})
onMounted(() => {
  getResponsiblePersons()
  getDesignPartList()
})
</script>

<style scoped lang="less">
.part-no-container {
  position: relative;

  .bom-tag {
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    .bom-tag {
      opacity: 1;
    }
  }
}
</style>

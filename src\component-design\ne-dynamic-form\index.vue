<template>
  <el-form v-bind="otherProps" :model="formModel" ref="ruleFormRef">
    <el-row :gutter="rowGutter">
      <el-col
        v-for="(item, index) in visibleItems"
        :key="index"
        :span="item.span || defaultSpan"
        :xs="item.xs"
        :sm="item.sm"
        :md="item.md"
        :lg="item.lg"
        :xl="item.xl"
        :offset="item.offset"
      >
        <el-form-item v-bind="item">
          <component :is="renderComponent(item)" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label=" ">
      <slot />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, h, ref, watch, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import {
  ElInput,
  ElSelect,
  ElOption,
  ElCheckbox,
  ElCheckboxGroup,
  ElRadio,
  ElRadioGroup,
  ElSwitch,
  ElDatePicker,
  ElTimePicker,
  ElCol,
  ElRow,
  ElCascader,
  ElSlider,
  ElRate,
  ElUpload,
  ElButton,
  FormProps,
  FormInstance
} from 'element-plus'
import { get, set } from 'lodash-es'

export interface DynamicFormExposeProps {
  formIns?: FormInstance
  getFormData: () => any
}
export interface SchemaItemProps {
  type: string
  label: string
  prop: string
  placeholder?: string
  modelValue?: any
  options?: Array<{ label: string; value: string | number }> | string[]
  span?: number
  xs?: number | object
  sm?: number | object
  md?: number | object
  lg?: number | object
  xl?: number | object
  offset?: number
  hidden?: boolean | ((formModel: any) => boolean)
  disabled?: boolean | ((formModel: any) => boolean)
  [x: string]: any
}
type NewFormProps = FormProps & {
  schema: Array<SchemaItemProps>
  rowGutter?: number
  defaultSpan?: number
}
const ruleFormRef = ref<FormInstance>()
const props = withDefaults(defineProps<Partial<NewFormProps>>(), {
  labelWidth: 'auto',
  showMessage: true,
  rowGutter: 20,
  defaultSpan: 24
})
let { schema, model, rowGutter, defaultSpan, ...otherProps } = props
let formModel = reactive(
  model ||
    schema?.reduce((pre, item) => {
      set(pre, item.prop, item?.modelValue || undefined)
      return pre
    }, {} as any)
)
let newSchema = ref<NewFormProps['schema']>(schema as SchemaItemProps[])

// 处理表单项的显示和禁用状态
const visibleItems = computed(() => {
  if (!newSchema.value || newSchema.value.length === 0) return []

  // 过滤掉隐藏的表单项
  return newSchema.value.filter((item) => {
    // 处理隐藏状态
    if (typeof item.hidden === 'function') {
      return !item.hidden(formModel)
    }

    if (item.hidden === true) {
      return false
    }

    // 处理禁用状态
    if (typeof item.disabled === 'function') {
      item.disabled = item.disabled(formModel)
    }

    return true
  })
})
watch(
  () => props.schema,
  (newVal: SchemaItemProps[] | undefined) => {
    newSchema.value = newVal || []
    newVal?.forEach((item) => {
      let { prop, modelValue } = item
      modelValue && set(formModel, prop, modelValue)
    })
  },
  { deep: true }
)
const renderComponent = (item: any) => {
  const { prop, type, ...otherProps } = item
  const modelValue = Array.isArray(prop) ? undefined : get(formModel, prop)
  const updateValue = (val: any) => {
    if (!Array.isArray(prop)) {
      set(formModel, prop, val)
    }
  }
  let placeholderStr: any = {
    input: '请输入',
    textarea: '请输入',
    select: '请选择'
  }
  const props = {
    placeholder: placeholderStr[type] || '请输入',
    ...otherProps,
    modelValue,
    name: prop,
    'onUpdate:modelValue': updateValue
  }
  switch (type) {
    case 'input':
      return h(ElInput, props)
    case 'textarea':
      return h(ElInput, {
        type: 'textarea',
        ...props
      })
    case 'select':
      return h(
        ElSelect,
        props,
        (item.options || []).map((opt: any) =>
          h(ElOption, { label: opt.label, value: opt.value })
        )
      )
    case 'checkbox':
      return h(
        ElCheckboxGroup,
        props,
        (item.options || []).map((opt: string) =>
          h(ElCheckbox, { label: opt }, () => opt)
        )
      )
    case 'radio':
      return h(
        ElRadioGroup,
        props,
        (item.options || []).map((opt: string) => h(ElRadio, { label: opt }, () => opt))
      )
    case 'switch':
      return h(ElSwitch, props)
    case 'daterange':
      return h(ElDatePicker, {
        type: 'daterange',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
        ...props,
        style: 'width: 100%'
      })
    case 'date-picker':
      return h(ElDatePicker, {
        ...props,
        style: 'width: 100%'
      })
    case 'time-picker':
      return h(ElTimePicker, {
        ...props,
        style: 'width: 100%'
      })
    case 'date-time-range':
      return h('div', { class: 'flex gap-2 w-full' }, [
        h(ElCol, { span: 11 }, () =>
          h(ElDatePicker, {
            modelValue: formModel[prop[0]],
            'onUpdate:modelValue': (val: any) => (formModel[prop[0]] = val),
            type: 'date',
            placeholder: 'Pick a date',
            style: 'width: 100%'
          })
        ),
        h(ElCol, { span: 2 }, () => h('span', { class: 'text-gray-500' }, '-')),
        h(ElCol, { span: 11 }, () =>
          h(ElTimePicker, {
            modelValue: formModel[prop[1]],
            'onUpdate:modelValue': (val: any) => (formModel[prop[1]] = val),
            placeholder: 'Pick a time',
            style: 'width: 100%'
          })
        )
      ])
    case 'cascader':
      return h(ElCascader, {
        ...props,
        style: 'width: 100%'
      })
    case 'slider':
      return h(ElSlider, props)
    case 'rate':
      return h(ElRate, props)
    case 'upload':
      return h(
        ElUpload,
        {
          ...props,
          'onUpdate:fileList': updateValue
        },
        {
          default: () =>
            h(
              ElButton,
              { type: 'primary' },
              {
                default: () => '点击上传',
                icon: () => h(Plus)
              }
            ),
          tip: () => (item.tip ? h('div', { class: 'el-upload__tip' }, item.tip) : null)
        }
      )
    default:
      return h('div', {}, 'Unsupported field')
  }
}
const emit = defineEmits(['onValuesChange'])
watch(
  formModel,
  (newVal) => {
    emit('onValuesChange', newVal)
  },
  { deep: true }
)
defineExpose({
  formIns: ruleFormRef,
  getFormData: () => {
    return formModel
  }
})
defineOptions({
  inheritAttrs: false
})
</script>

<style scoped>
:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: normal;
}
</style>
